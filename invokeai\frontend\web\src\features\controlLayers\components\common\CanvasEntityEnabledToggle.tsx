import { IconButton } from '@invoke-ai/ui-library';
import { useStore } from '@nanostores/react';
import { useAppDispatch } from 'app/store/storeHooks';
import { useCanvasManager } from 'features/controlLayers/contexts/CanvasManagerProviderGate';
import { useEntityIdentifierContext } from 'features/controlLayers/contexts/EntityIdentifierContext';
import { useCanvasIsBusy } from 'features/controlLayers/hooks/useCanvasIsBusy';
import { useEntityIsEnabled } from 'features/controlLayers/hooks/useEntityIsEnabled';
import { entityIsEnabledToggled } from 'features/controlLayers/store/canvasSlice';
import { memo, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PiCircleBold, PiCircleFill } from 'react-icons/pi';

export const CanvasEntityEnabledToggle = memo(() => {
  const { t } = useTranslation();
  const entityIdentifier = useEntityIdentifierContext();
  const isEnabled = useEntityIsEnabled(entityIdentifier);
  const isBusy = useCanvasIsBusy();
  const canvasManager = useCanvasManager();
  const isStaging = useStore(canvasManager.stagingArea.$isStaging);
  const dispatch = useAppDispatch();

  const onClick = useCallback(() => {
    dispatch(entityIsEnabledToggled({ entityIdentifier }));
  }, [dispatch, entityIdentifier]);

  // Allow enabling/disabling these entity types even during staging
  const allowedDuringStaging = useMemo(() => {
    return entityIdentifier.type === 'control_layer' ||
           entityIdentifier.type === 'reference_image' ||
           entityIdentifier.type === 'regional_guidance';
  }, [entityIdentifier.type]);

  // If staging is active and this entity type is allowed during staging,
  // only disable for non-staging busy states
  const isDisabled = useMemo(() => {
    if (isStaging && allowedDuringStaging) {
      // During staging, only disable for non-staging busy states
      return canvasManager.stateApi.$isFiltering.get() ||
             canvasManager.stateApi.$isTransforming.get() ||
             canvasManager.stateApi.$isRasterizing.get() ||
             canvasManager.stateApi.$isSegmenting.get() ||
             canvasManager.compositor.$isBusy.get();
    }
    // For other entity types or when not staging, use the full busy state
    return isBusy;
  }, [isStaging, allowedDuringStaging, canvasManager, isBusy]);

  return (
    <IconButton
      size="sm"
      aria-label={t(isEnabled ? 'common.enabled' : 'common.disabled')}
      tooltip={t(isEnabled ? 'common.enabled' : 'common.disabled')}
      variant="link"
      alignSelf="stretch"
      icon={isEnabled ? <PiCircleFill /> : <PiCircleBold />}
      onClick={onClick}
      isDisabled={isDisabled}
    />
  );
});

CanvasEntityEnabledToggle.displayName = 'CanvasEntityEnabledToggle';
